package com.jurassic.myhealth.service.healthcare.data.gateway

import com.jurassic.myhealth.service.healthcare.data.client.luxmed.LuxMedClient
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.EventDetailsRequest
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.LuxMedEventTypeRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.TimelineEventRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.TimelineRequest
import com.jurassic.myhealth.service.healthcare.data.gateway.mapper.LuxMedEventMapper
import com.jurassic.myhealth.service.healthcare.data.gateway.mapper.LuxMedLabResultMapper
import com.jurassic.myhealth.service.healthcare.domain.gateway.LuxmedMedGateway
import com.jurassic.myhealth.service.healthcare.domain.gateway.LuxmedMedicalData
import com.jurassic.myhealth.service.healthcare.domain.model.LabResult
import com.jurassic.myhealth.service.healthcare.domain.model.luxmed.LuxMedAuthentication
import com.jurassic.myhealth.service.healthcare.domain.model.UserId
import mu.KotlinLogging
import org.springframework.stereotype.Component

private val logger = KotlinLogging.logger {}

@Component
class LuxmedMedGatewayImpl(
    private val luxMedClient: LuxMedClient,
    private val eventMapper: LuxMedEventMapper,
    private val labResultMapper: LuxMedLabResultMapper
) : LuxmedMedGateway {

    override suspend fun fetchAllResults(
        userId: UserId,
        luxmedAuthentication: LuxMedAuthentication
    ): Result<LuxmedMedicalData> =
        try {
            logger.info { "Fetching medical data from LuxMed for user: $userId" }

            val medicalEventTypes = listOf(
                LuxMedEventTypeRemote.LABORATORY_TEST,
                LuxMedEventTypeRemote.VISIT,
                LuxMedEventTypeRemote.TELEMEDICINE_VISIT,
                LuxMedEventTypeRemote.PHONE_CONSULTATION,
                LuxMedEventTypeRemote.EXAMINATION
            )

            val timelineRequest = TimelineRequest.withEventTypes(medicalEventTypes)
            val timelineResponse = luxMedClient.getTimelineEvents(luxmedAuthentication, timelineRequest)

            logger.debug { "Fetched ${timelineResponse.events.size} events from LuxMed" }

            val labEvents = timelineResponse.events.filter {
                it.eventType == LuxMedEventTypeRemote.LABORATORY_TEST.id
            }
            val otherEvents = timelineResponse.events.filter {
                it.eventType != LuxMedEventTypeRemote.LABORATORY_TEST.id
            }

            val labResults = fetchDetailedLabResults(userId, luxmedAuthentication, labEvents)

            val (_, visits) = eventMapper.mapEvents(userId, otherEvents)

            logger.info {
                "Mapped LuxMed data for user $userId: ${labResults.size} lab results, ${visits.size} visits"
            }

            val medicalData = LuxmedMedicalData(
                labResults = labResults,
                visits = visits
            )

            Result.success(medicalData)
        } catch (e: Exception) {
            logger.error(e) { "Failed to fetch medical data from LuxMed for user: $userId" }
            Result.failure(e)
        }

    /**
     * Fetch detailed lab results with TestResultRemote data
     */
    private suspend fun fetchDetailedLabResults(
        userId: UserId,
        luxmedAuthentication: LuxMedAuthentication,
        labEvents: List<TimelineEventRemote>
    ): List<LabResult> {
        val labResults = mutableListOf<LabResult>()

        labEvents.forEach { event ->
            try {
                logger.info { "Processing lab event ${event.eventId}: title='${event.title}', date='${event.date}', status=${event.status}" }

                val eventDetailsRequest = EventDetailsRequest.forEvent(
                    eventId = event.eventId,
                    eventType = LuxMedEventTypeRemote.LABORATORY_TEST,
                    assignedFromReservationId = event.assignedFromReservationId
                )

                logger.debug { "Fetching event details for lab event ${event.eventId}" }
                val eventDetailsResponse = luxMedClient.getEventDetails(luxmedAuthentication, eventDetailsRequest)
                logger.debug { "Received event details for lab event ${event.eventId}: eventType=${eventDetailsResponse.eventType}, status=${eventDetailsResponse.status}" }

                val labResult = labResultMapper.mapLabResultFromEventDetails(userId, eventDetailsResponse, event.eventId)

                if (labResult != null) {
                    labResults.add(labResult)
                    logger.info { "Successfully processed lab result for event ${event.eventId}: testName='${labResult.testName}', parameters=${labResult.parameters.size}" }
                } else {
                    logger.warn { "Failed to map lab result for event ${event.eventId}. Event details: title='${event.title}', status=${event.status}" }
                }

            } catch (e: Exception) {
                logger.error(e) { "Failed to fetch detailed lab result for event ${event.eventId}" }
                // Continue with other events even if one fails
            }
        }

        return labResults
    }
}
