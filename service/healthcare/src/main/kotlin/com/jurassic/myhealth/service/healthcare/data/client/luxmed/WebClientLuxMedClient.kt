package com.jurassic.myhealth.service.healthcare.data.client.luxmed

import com.fasterxml.jackson.databind.ObjectMapper
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.EventDetailsRequest
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.EventDetailsResponse
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.TimelineRequest
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.TimelineResponse
import com.jurassic.myhealth.service.healthcare.domain.model.luxmed.LuxMedAuthentication
import mu.KotlinLogging
import org.springframework.http.HttpHeaders
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.awaitBody
import org.springframework.web.util.UriComponentsBuilder
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.util.zip.GZIPInputStream

/**
 * WebClient implementation of the LuxMed API client
 *
 * Implements LuxMed Patient Portal API authentication requirements based on analysis:
 * - Essential authentication cookies (ASP.NET_SessionId, LXToken, RefreshToken, UserAdditionalInfo)
 * - CSRF protection with XSRF-TOKEN
 * - Browser-like headers for authenticity
 * - Proper JWT token handling in authorization-token header
 */
class WebClientLuxMedClient(
    private val webClient: WebClient
) : LuxMedClient {

    private val objectMapper = ObjectMapper()
    private val logger = KotlinLogging.logger {}

    /**
     * Sanitizes JSON response by removing control characters and extracting valid JSON content
     */
    private fun sanitizeJsonResponse(jsonString: String): String {
        logger.info { "Original response length: ${jsonString.length}" }
        logger.info { "First 50 chars: ${jsonString.take(50).map { if (it.isISOControl()) "\\u${it.code.toString(16).padStart(4, '0')}" else it.toString() }.joinToString("")}" }

        // First, remove control characters and invalid Unicode
        var cleaned = jsonString
            // Remove control characters (ASCII 0-31 except whitespace: \t, \n, \r)
            .replace(Regex("[\u0000-\u0008\u000B\u000C\u000E-\u001F]"), "")
            // Remove Unicode replacement character (0xFFFD)
            .replace("\uFFFD", "")
            // Remove other problematic Unicode characters
            .replace(Regex("[\uFEFF\u200B-\u200D\u2060\uFFF0-\uFFFF]"), "")
            // Ensure proper UTF-8 encoding by removing any remaining non-printable characters
            .replace(Regex("[\\p{Cntrl}&&[^\r\n\t]]"), "")

        // Find the start of actual JSON content (look for { or [)
        val jsonStartIndex = cleaned.indexOfFirst { it == '{' || it == '[' }
        if (jsonStartIndex > 0) {
            logger.info { "Found JSON start at index: $jsonStartIndex, removing prefix: ${cleaned.take(jsonStartIndex)}" }
            cleaned = cleaned.substring(jsonStartIndex)
        }

        // Find the end of JSON content (last } or ])
        val jsonEndIndex = cleaned.indexOfLast { it == '}' || it == ']' }
        if (jsonEndIndex >= 0 && jsonEndIndex < cleaned.length - 1) {
            logger.info { "Found JSON end at index: $jsonEndIndex, removing suffix: ${cleaned.substring(jsonEndIndex + 1)}" }
            cleaned = cleaned.substring(0, jsonEndIndex + 1)
        }

        logger.info { "Final sanitized response length: ${cleaned.length}" }
        logger.info { "First 100 chars after sanitization: ${cleaned.take(100)}" }

        return cleaned
    }

    /**
     * Gets response as byte array, decompresses if needed, sanitizes, and parses to specified type
     */
    private suspend inline fun <reified T> WebClient.RequestHeadersSpec<*>.retrieveAndParse(): T {
        val responseBytes = this.retrieve().awaitBody<ByteArray>()
        val decompressedString = decompressIfNeeded(responseBytes)
        val sanitizedJson = sanitizeJsonResponse(decompressedString)
        return objectMapper.readValue(sanitizedJson, T::class.java)
    }

    /**
     * Decompresses GZIP data if the response is compressed
     */
    private fun decompressIfNeeded(data: ByteArray): String {
        return if (data.size >= 2 && data[0] == 0x1f.toByte() && data[1] == 0x8b.toByte()) {
            // GZIP magic number detected
            logger.info { "GZIP compressed response detected, decompressing..." }
            try {
                val gzipInputStream = GZIPInputStream(ByteArrayInputStream(data))
                val outputStream = ByteArrayOutputStream()
                gzipInputStream.copyTo(outputStream)
                val decompressed = outputStream.toString("UTF-8")
                logger.info { "Decompressed response length: ${decompressed.length}" }
                decompressed
            } catch (e: Exception) {
                logger.error(e) { "Failed to decompress GZIP response, treating as plain text" }
                String(data, Charsets.UTF_8)
            }
        } else {
            // Not compressed
            String(data, Charsets.UTF_8)
        }
    }

    companion object {
        private const val BASE_URL = "https://portalpacjenta.luxmed.pl/PatientPortal/NewPortal"
        private const val TIMELINE_EVENTS_PATH = "/Timeline/getevents"
        private const val EVENT_DETAILS_PATH = "/Timeline/geteventdetails"
        private const val AUTH_HEADER = "authorization-token"
        private const val REFERER_URL = "https://portalpacjenta.luxmed.pl/PatientPortal/NewPortal/Page/Timeline"
        private const val USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********"
    }



    /**
     * Configures WebClient request with all required LuxMed authentication headers and cookies
     */
    private fun WebClient.RequestHeadersSpec<*>.configureLuxMedAuth(authentication: LuxMedAuthentication): WebClient.RequestHeadersSpec<*> {
        return this
            // Essential authentication header
            .header(AUTH_HEADER, "Bearer ${authentication.jwtToken}")

            // Essential cookies for authentication
            .cookie("ASP.NET_SessionId", authentication.aspNetSessionId)
            .cookie("LXToken", authentication.lxToken)
            .cookie("RefreshToken", authentication.refreshToken)
            .cookie("UserAdditionalInfo", authentication.userAdditionalInfo)
            .cookie("XSRF-TOKEN", authentication.xsrfToken)

            // Optional security cookies
            .apply {
                authentication.incapsulaSessionId?.let { cookie("incap_ses_", it) }
                authentication.deviceId?.let { cookie("DeviceId", it) }
            }

            // Browser-like headers for authenticity
            .header(HttpHeaders.ACCEPT, "application/json, text/plain, */*")
            .header(HttpHeaders.ACCEPT_LANGUAGE, "pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7")
            .header(HttpHeaders.ACCEPT_ENCODING, "gzip, deflate, br")
            .header(HttpHeaders.CACHE_CONTROL, "no-cache")
            .header("pragma", "no-cache")
            .header("priority", "u=1, i")
            .header(HttpHeaders.REFERER, REFERER_URL)
            .header("sec-ch-ua", "\"Not(A:Brand\";v=\"99\", \"Opera\";v=\"118\", \"Chromium\";v=\"133\"")
            .header("sec-ch-ua-mobile", "?0")
            .header("sec-ch-ua-platform", "\"macOS\"")
            .header("sec-fetch-dest", "empty")
            .header("sec-fetch-mode", "cors")
            .header("sec-fetch-site", "same-origin")
            .header(HttpHeaders.USER_AGENT, USER_AGENT)
            .header("x-requested-with", "XMLHttpRequest")
            .header("X-XSRF-TOKEN", authentication.xsrfToken) // CSRF protection
    }

    /**
     * Get timeline events
     * @param authentication LuxMed authentication context with all required components
     * @param request Timeline request parameters
     * @return Timeline response with events
     */
    override suspend fun getTimelineEvents(authentication: LuxMedAuthentication, request: TimelineRequest): TimelineResponse {
        val uri = UriComponentsBuilder.fromUriString(BASE_URL + TIMELINE_EVENTS_PATH)
            // LuxMed API expects all parameters to be present, even if null
            .queryParam("eventTypeIds", request.eventTypeIds ?: "null")
            .queryParam("dateFrom", request.dateFrom ?: "null")
            .queryParam("dateTo", request.dateTo ?: "null")
            .queryParam("beforeDate", request.beforeDate ?: "null")
            .build()
            .toUri()

        return webClient.get()
            .uri(uri)
            .configureLuxMedAuth(authentication)
            .retrieveAndParse()
    }

    /**
     * Get event details
     * @param authentication LuxMed authentication context with all required components
     * @param request Event details request parameters
     * @return Event details response
     */
    override suspend fun getEventDetails(authentication: LuxMedAuthentication, request: EventDetailsRequest): EventDetailsResponse {
        val uri = UriComponentsBuilder.fromUriString(BASE_URL + EVENT_DETAILS_PATH)
            .queryParam("eventId", request.eventId)
            .queryParam("eventType", request.eventType)
            .queryParam("assignedFromReservationId", request.assignedFromReservationId)
            .build()
            .toUri()

        return webClient.get()
            .uri(uri)
            .configureLuxMedAuth(authentication)
            .retrieveAndParse()
    }

    /**
     * Get event details as raw JSON string
     * @param authentication LuxMed authentication context with all required components
     * @param request Event details request parameters
     * @return Raw JSON response as string
     */
    override suspend fun getEventDetailsRaw(authentication: LuxMedAuthentication, request: EventDetailsRequest): String {
        val uri = UriComponentsBuilder.fromUriString(BASE_URL + EVENT_DETAILS_PATH)
            .queryParam("eventId", request.eventId)
            .queryParam("eventType", request.eventType)
            .queryParam("assignedFromReservationId", request.assignedFromReservationId)
            .build()
            .toUri()

        val responseBytes = webClient.get()
            .uri(uri)
            .configureLuxMedAuth(authentication)
            .retrieve()
            .awaitBody<ByteArray>()

        val decompressedString = decompressIfNeeded(responseBytes)
        return sanitizeJsonResponse(decompressedString)
    }
}
