package com.jurassic.myhealth.service.healthcare.data.repository

import com.jurassic.myhealth.service.healthcare.data.repository.mapper.LabResultMapper
import com.jurassic.myhealth.service.healthcare.domain.model.LabResult
import com.jurassic.myhealth.service.healthcare.domain.model.SourceSystem
import com.jurassic.myhealth.service.healthcare.domain.model.UserId
import com.jurassic.myhealth.service.healthcare.domain.repository.LabResultRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.reactive.awaitSingle
import mu.KotlinLogging
import org.springframework.stereotype.Repository

private val logger = KotlinLogging.logger {}

/**
 * Implementation of LabResultRepository using MongoDB
 */
@Repository
class LabResultRepositoryImpl(
    private val mongoRepository: MongoLabResultRepository,
    private val mapper: LabResultMapper
) : LabResultRepository {

    override suspend fun save(labResult: LabResult): LabResult {
        return try {
            logger.debug { "Saving lab result: userId=${labResult.userId.value}, externalId=${labResult.externalId}, testName='${labResult.testName}'" }
            val document = mapper.toDocument(labResult)
            val savedDocument = mongoRepository.save(document).awaitSingle()
            val result = mapper.toDomain(savedDocument)
            logger.debug { "Successfully saved lab result with id: ${result.id}" }
            result
        } catch (e: Exception) {
            logger.warn(e) { "Failed to save lab result: userId=${labResult.userId.value}, externalId=${labResult.externalId}" }
            // If it's a duplicate key error, try to find and update the existing document
            if (e.message?.contains("duplicate key") == true) {
                logger.info { "Attempting to update existing lab result due to duplicate key: userId=${labResult.userId.value}, externalId=${labResult.externalId}" }
                val existingDocument = mongoRepository.findByUserIdAndSourceSystemAndExternalId(
                    labResult.userId.value,
                    labResult.sourceSystem.name,
                    labResult.externalId
                )
                if (existingDocument != null) {
                    logger.debug { "Found existing document with id: ${existingDocument.id}, updating..." }
                    // Update the existing document with new data
                    val updatedDocument = mapper.toDocument(labResult.copy(id = existingDocument.id))
                    val savedDocument = mongoRepository.save(updatedDocument).awaitSingle()
                    val result = mapper.toDomain(savedDocument)
                    logger.info { "Successfully updated existing lab result with id: ${result.id}" }
                    result
                } else {
                    logger.error { "Duplicate key error but no existing document found for userId=${labResult.userId.value}, externalId=${labResult.externalId}" }
                    throw e
                }
            } else {
                logger.error(e) { "Unexpected error saving lab result: userId=${labResult.userId.value}, externalId=${labResult.externalId}" }
                throw e
            }
        }
    }

    override suspend fun saveAll(labResults: List<LabResult>): List<LabResult> {
        val documents = labResults.map { mapper.toDocument(it) }
        val savedDocuments = mongoRepository.saveAll(documents).collectList().awaitSingle()
        return savedDocuments.map { mapper.toDomain(it) }
    }

    override fun findByUserId(userId: UserId): Flow<LabResult> {
        return mongoRepository.findByUserId(userId.value)
            .map { mapper.toDomain(it) }
    }

    override suspend fun findByUserIdAndSourceSystemAndExternalId(
        userId: UserId,
        sourceSystem: SourceSystem,
        externalId: String
    ): LabResult? {
        val document = mongoRepository.findByUserIdAndSourceSystemAndExternalId(
            userId.value,
            sourceSystem.name,
            externalId
        )
        return document?.let { mapper.toDomain(it) }
    }

    override suspend fun existsByUserIdAndSourceSystemAndExternalId(
        userId: UserId,
        sourceSystem: SourceSystem,
        externalId: String
    ): Boolean {
        return mongoRepository.existsByUserIdAndSourceSystemAndExternalId(
            userId.value,
            sourceSystem.name,
            externalId
        )
    }

    override suspend fun deleteByUserIdAndSourceSystem(userId: UserId, sourceSystem: SourceSystem) {
        mongoRepository.deleteByUserIdAndSourceSystem(userId.value, sourceSystem.name)
    }
}
