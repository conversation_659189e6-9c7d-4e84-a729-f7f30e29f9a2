package com.jurassic.myhealth.service.healthcare.data.gateway.mapper

import com.fasterxml.jackson.databind.ObjectMapper
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.ExaminationRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.LabResultResponse
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.LabResultStatusRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.TestResultRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.EventDetailsResponse
import com.jurassic.myhealth.service.healthcare.domain.model.*
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

private val logger = KotlinLogging.logger {}

/**
 * Mapper for converting LuxMed laboratory results to domain models
 */
@Component
class LuxMedLabResultMapper(
    private val objectMapper: ObjectMapper
) {
    
    private val dateTimeFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
    
    fun mapLabResultFromEventDetails(
        userId: UserId,
        eventDetailsResponse: EventDetailsResponse
    ): LabResult? {
        return try {
            logger.debug { "Starting lab result mapping for event ${eventDetailsResponse.id}, eventType: ${eventDetailsResponse.eventType}" }
            logger.debug { "Event details raw data: title='${eventDetailsResponse.title}', date='${eventDetailsResponse.date}', status=${eventDetailsResponse.status}" }

            val labResultResponse = convertToLabResultResponse(eventDetailsResponse)
            logger.debug { "Successfully converted EventDetailsResponse to LabResultResponse for event ${eventDetailsResponse.id}" }
            logger.debug { "LabResultResponse: eventId=${labResultResponse.eventId}, examinations.size=${labResultResponse.examinations.size}, title='${labResultResponse.title}'" }

            val result = mapLabResult(userId, labResultResponse)
            if (result != null) {
                logger.debug { "Successfully mapped lab result for event ${eventDetailsResponse.id}: testName='${result.testName}', parameters.size=${result.parameters.size}" }
            } else {
                logger.warn { "Lab result mapping returned null for event ${eventDetailsResponse.id}" }
            }
            result
        } catch (e: Exception) {
            logger.error(e) { "Failed to map lab result from event details for event ${eventDetailsResponse.id}. Event data: ${eventDetailsResponse}" }
            null
        }
    }
    
    /**
     * Convert LabResultResponse to domain LabResult
     */
    fun mapLabResult(userId: UserId, labResultResponse: LabResultResponse): LabResult? {
        logger.debug { "Starting mapLabResult for eventId: ${labResultResponse.eventId}" }

        if (labResultResponse.eventId == 0L) {
            logger.warn { "Skipping lab result with eventId=0" }
            return null
        }

        val observationDate = parseDateTime(labResultResponse.date)
        if (observationDate == null) {
            logger.warn { "Failed to parse observation date '${labResultResponse.date}' for eventId: ${labResultResponse.eventId}" }
            return null
        }
        logger.debug { "Parsed observation date: $observationDate for eventId: ${labResultResponse.eventId}" }

        logger.debug { "Processing ${labResultResponse.examinations.size} examinations for eventId: ${labResultResponse.eventId}" }
        val parameters = labResultResponse.examinations.flatMapIndexed { index, examination ->
            logger.debug { "Processing examination $index: name='${examination.name}', results.size=${examination.results.size}, availabilityStatus=${examination.availabilityStatus}" }
            val params = mapExaminationToParameters(examination, observationDate)
            logger.debug { "Examination $index mapped to ${params.size} parameters" }
            params
        }
        logger.debug { "Total parameters mapped: ${parameters.size} for eventId: ${labResultResponse.eventId}" }

        val testName = labResultResponse.examinations.firstOrNull()?.name?.takeIf { it.isNotBlank() }
            ?: labResultResponse.title.ifBlank { "Laboratory Test" }
        logger.debug { "Test name determined: '$testName' for eventId: ${labResultResponse.eventId}" }

        return try {
            val labResult = LabResult(
                userId = userId,
                externalId = labResultResponse.eventId.toString(),
                sourceSystem = SourceSystem.LUXMED,
                testName = testName,
                parameters = parameters,
                observationDate = observationDate,
                status = mapEventStatus(labResultResponse.status),
                reportUrl = null, // Could be populated from results.downloadUrl if available
                notes = labResultResponse.examinations.firstOrNull()?.comment?.comment
            )
            logger.debug { "Successfully created LabResult domain object for eventId: ${labResultResponse.eventId}" }
            labResult
        } catch (e: Exception) {
            logger.error(e) { "Failed to create LabResult domain object for eventId: ${labResultResponse.eventId}. TestName: '$testName', Parameters: ${parameters.size}, Status: ${mapEventStatus(labResultResponse.status)}" }
            null
        }
    }
    
    /**
     * Map ExaminationRemote to list of LabParameters
     */
    private fun mapExaminationToParameters(examination: ExaminationRemote, observationDate: LocalDateTime): List<LabParameter> {
        logger.debug { "Mapping examination '${examination.name}' with ${examination.results.size} results" }

        val parameters = examination.results.mapIndexedNotNull { index, testResult ->
            logger.debug { "Processing test result $index: name='${testResult.name}', value='${testResult.value}', status=${testResult.status}, measurement='${testResult.measurement}'" }
            val parameter = mapTestResultToParameter(testResult, observationDate)
            if (parameter != null) {
                logger.debug { "Successfully mapped test result $index to parameter: ${parameter.name}" }
            } else {
                logger.debug { "Test result $index was filtered out (likely INFORMATION status)" }
            }
            parameter
        }

        logger.debug { "Examination '${examination.name}' mapped to ${parameters.size} parameters" }
        return parameters
    }
    
    /**
     * Map TestResultRemote to LabParameter
     */
    private fun mapTestResultToParameter(testResult: TestResultRemote, observationDate: LocalDateTime): LabParameter? {
        if (testResult.status == LabResultStatusRemote.INFORMATION.id) {
            logger.debug { "Skipping test result '${testResult.name}' with INFORMATION status" }
            return null
        }

        val interpretation = mapResultStatus(testResult.status)
        logger.debug { "Mapping test result: name='${testResult.name}', value='${testResult.value}', status=${testResult.status} -> interpretation='$interpretation'" }

        return try {
            val parameter = LabParameter(
                name = testResult.name,
                value = testResult.value,
                unit = testResult.measurement.takeIf { it.isNotBlank() },
                min = testResult.minScopeFormatted?.takeIf { it.isNotBlank() },
                max = testResult.maxScopeFormatted?.takeIf { it.isNotBlank() },
                date = observationDate,
                interpretation = interpretation,
                notes = null
            )
            logger.debug { "Successfully created LabParameter: ${parameter.name} = ${parameter.value} ${parameter.unit ?: ""}" }
            parameter
        } catch (e: Exception) {
            logger.error(e) { "Failed to create LabParameter from test result: name='${testResult.name}', value='${testResult.value}'" }
            null
        }
    }

    /**
     * Map result status code to interpretation string
     */
    private fun mapResultStatus(statusCode: Int): String? {
        return when (LabResultStatusRemote.fromId(statusCode)) {
            LabResultStatusRemote.NORMAL -> "Normal"
            LabResultStatusRemote.ABNORMAL -> "Abnormal"
            LabResultStatusRemote.CRITICAL -> "Critical"
            LabResultStatusRemote.INFORMATION -> "Information"
            LabResultStatusRemote.UNKNOWN -> null
        }
    }
    
    /**
     * Convert EventDetailsResponse to LabResultResponse
     * This is needed because the API returns different structures for different event types
     */
    private fun convertToLabResultResponse(eventDetailsResponse: EventDetailsResponse): LabResultResponse {
        return try {
            logger.debug { "Converting EventDetailsResponse to LabResultResponse for event ${eventDetailsResponse.id}" }
            val jsonString = objectMapper.writeValueAsString(eventDetailsResponse)
            logger.debug { "EventDetailsResponse JSON: $jsonString" }

            val labResultResponse = objectMapper.readValue(jsonString, LabResultResponse::class.java)
            logger.debug { "Conversion successful: eventId=${labResultResponse.eventId}, examinations.size=${labResultResponse.examinations.size}" }
            labResultResponse
        } catch (e: Exception) {
            logger.error(e) { "Failed to convert EventDetailsResponse to LabResultResponse for event ${eventDetailsResponse.id}" }
            throw e
        }
    }
    
    private fun parseDateTime(dateString: String): LocalDateTime? {
        return try {
            logger.debug { "Parsing date string: '$dateString'" }
            val parsedDate = LocalDateTime.parse(dateString, dateTimeFormatter)
            logger.debug { "Successfully parsed date: $parsedDate" }
            parsedDate
        } catch (e: Exception) {
            logger.warn(e) { "Failed to parse date: '$dateString' using formatter: $dateTimeFormatter" }
            null
        }
    }
    
    private fun mapEventStatus(statusCode: Int): String {
        return when (statusCode) {
            1 -> "Completed"
            2 -> "Scheduled"
            3 -> "Cancelled"
            4 -> "In Progress"
            5 -> "No Show"
            else -> "Unknown"
        }
    }
}
