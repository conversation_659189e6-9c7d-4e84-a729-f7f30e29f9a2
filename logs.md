2025-06-22T17:42:14.091+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] c.j.m.s.MyHealthServiceApplicationKt     : Starting MyHealthServiceApplicationKt using Java 17.0.14 with PID 35549 (/Users/<USER>/IdeaProjects/MyHealth-Service/service/build/classes/kotlin/main started by dominikjura in /Users/<USER>/IdeaProjects/MyHealth-Service)
2025-06-22T17:42:14.095+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] c.j.m.s.MyHealthServiceApplicationKt     : No active profile set, falling back to 1 default profile: "default"
2025-06-22T17:42:14.139+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-22T17:42:14.139+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-22T17:42:14.790+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-22T17:42:14.797+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 0 MongoDB repository interfaces.
2025-06-22T17:42:14.798+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Reactive MongoDB repositories in DEFAULT mode.
2025-06-22T17:42:14.904+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 104 ms. Found 2 Reactive MongoDB repository interfaces.
2025-06-22T17:42:15.667+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] org.mongodb.driver.client                : MongoClient with metadata {"driver": {"name": "mongo-java-driver|reactive-streams|spring-data", "version": "5.4.0"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "14.5"}, "platform": "Java/Amazon.com Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@7a758a1d, com.mongodb.Jep395RecordCodecProvider@5302f779, com.mongodb.KotlinCodecProvider@74b35fb0]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[127.0.0.1:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-22T17:42:15.673+02:00  INFO 35549 --- [MyHealth-Service] [127.0.0.1:27017] org.mongodb.driver.cluster               : Monitor thread successfully connected to server with description ServerDescription{address=127.0.0.1:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=17729042, minRoundTripTimeNanos=0}
2025-06-22T17:42:15.704+02:00  WARN 35549 --- [MyHealth-Service] [  restartedMain] o.s.data.convert.CustomConversions       : Registering converter from interface java.util.List to interface org.springframework.data.domain.Vector as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
2025-06-22T17:42:15.726+02:00  WARN 35549 --- [MyHealth-Service] [  restartedMain] o.s.data.convert.CustomConversions       : Registering converter from interface java.util.List to interface org.springframework.data.domain.Vector as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
2025-06-22T17:42:15.966+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] c.j.m.s.a.d.i.firebase.FirebaseConfig    : Initializing Firebase app
2025-06-22T17:42:16.420+02:00  WARN 35549 --- [MyHealth-Service] [  restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-22T17:42:16.655+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] org.mongodb.driver.client                : MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.4.0"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "14.5"}, "platform": "Java/Amazon.com Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@7a758a1d, com.mongodb.Jep395RecordCodecProvider@5302f779, com.mongodb.KotlinCodecProvider@74b35fb0]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-22T17:42:16.663+02:00  INFO 35549 --- [MyHealth-Service] [localhost:27017] org.mongodb.driver.cluster               : Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=6352084, minRoundTripTimeNanos=0}
2025-06-22T17:42:16.675+02:00  WARN 35549 --- [MyHealth-Service] [  restartedMain] o.s.data.convert.CustomConversions       : Registering converter from interface java.util.List to interface org.springframework.data.domain.Vector as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
2025-06-22T17:42:16.676+02:00  WARN 35549 --- [MyHealth-Service] [  restartedMain] o.s.data.convert.CustomConversions       : Registering converter from interface java.util.List to interface org.springframework.data.domain.Vector as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
2025-06-22T17:42:16.745+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] ctiveUserDetailsServiceAutoConfiguration :

Using generated security password: bb4b64c7-5805-4604-accf-b2d733bf7c04

2025-06-22T17:42:16.863+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] f.a.AutowiredAnnotationBeanPostProcessor : Inconsistent constructor declaration on bean with name '_reactiveMethodSecurityConfiguration': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: org.springframework.security.config.annotation.method.configuration.ReactiveAuthorizationManagerMethodSecurityConfiguration(org.springframework.security.access.expression.method.MethodSecurityExpressionHandler,org.springframework.beans.factory.ObjectProvider,org.springframework.beans.factory.ObjectProvider)
2025-06-22T17:42:17.097+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint beneath base path '/actuator'
2025-06-22T17:42:17.197+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] org.mongodb.driver.client                : MongoClient with metadata {"driver": {"name": "mongo-java-driver|reactive-streams|spring-boot", "version": "5.4.0"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "14.5"}, "platform": "Java/Amazon.com Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=NettyTransportSettings{eventLoopGroup=io.netty.channel.nio.NioEventLoopGroup@bc9daf7, socketChannelClass=null, allocator=null, sslContext=null}, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@4dfdc8c6], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@7a758a1d, com.mongodb.Jep395RecordCodecProvider@5302f779, com.mongodb.KotlinCodecProvider@74b35fb0]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@78062bfd], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-22T17:42:17.284+02:00  INFO 35549 --- [MyHealth-Service] [localhost:27017] org.mongodb.driver.cluster               : Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=19863084, minRoundTripTimeNanos=0}
2025-06-22T17:42:17.437+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8080 (http)
2025-06-22T17:42:17.447+02:00  INFO 35549 --- [MyHealth-Service] [  restartedMain] c.j.m.s.MyHealthServiceApplicationKt     : Started MyHealthServiceApplicationKt in 3.662 seconds (process running for 4.236)
2025-06-22T17:42:27.907+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-2] c.j.m.s.auth.api.config.SecurityConfig   : Skipping authentication for public endpoint: /actuator/health
2025-06-22T17:42:27.924+02:00 DEBUG 35549 --- [MyHealth-Service] [     parallel-1] c.j.m.s.auth.api.config.SecurityConfig   : Skipping authentication for public endpoint: /actuator/health
2025-06-22T17:42:50.583+02:00  INFO 35549 --- [MyHealth-Service] [atcher-worker-1] .d.i.f.FirebaseTokenValidatorGatewayImpl : Firebase token validated for user: zb7UNjq1hjeHyluOnAhcexeVpWw2
2025-06-22T17:42:50.685+02:00 DEBUG 35549 --- [MyHealth-Service] [atcher-worker-3] c.j.m.s.auth.api.config.SecurityConfig   : User authenticated: 4f21e935-b145-48c2-a663-9dbc7c052777
2025-06-22T17:42:50.691+02:00  INFO 35549 --- [MyHealth-Service] [atcher-worker-1] .d.i.f.FirebaseTokenValidatorGatewayImpl : Firebase token validated for user: zb7UNjq1hjeHyluOnAhcexeVpWw2
2025-06-22T17:42:50.694+02:00 DEBUG 35549 --- [MyHealth-Service] [atcher-worker-1] c.j.m.s.auth.api.config.SecurityConfig   : User authenticated: 4f21e935-b145-48c2-a663-9dbc7c052777
2025-06-22T17:42:50.770+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.a.c.LuxmedSyncController       : Starting LuxMed synchronization for user: 4f21e935-b145-48c2-a663-9dbc7c052777
2025-06-22T17:42:50.772+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.LuxmedMedGatewayImpl       : Fetching medical data from LuxMed for user: 4f21e935-b145-48c2-a663-9dbc7c052777
2025-06-22T17:42:50.860+02:00 ERROR 35549 --- [MyHealth-Service] [ctor-http-nio-3] i.n.r.d.DnsServerAddressStreamProviders  : Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-06-22T17:42:51.451+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : GZIP compressed response detected, decompressing...
2025-06-22T17:42:51.453+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : Decompressed response length: 10965
2025-06-22T17:42:51.453+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : Original response length: 10965
2025-06-22T17:42:51.454+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : First 50 chars: {"events":[{"hasReferrals":true,"hasPrescription":
2025-06-22T17:42:51.457+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : Final sanitized response length: 10965
2025-06-22T17:42:51.457+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : First 100 chars after sanitization: {"events":[{"hasReferrals":true,"hasPrescription":true,"hasRecommendations":true,"visitId":607008466
2025-06-22T17:42:51.467+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.LuxmedMedGatewayImpl       : Fetched 20 events from LuxMed
2025-06-22T17:42:51.469+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.LuxmedMedGatewayImpl       : Processing lab event 567399806: title='Badanie laboratoryjne', date='2024-12-24T12:03:00', status=4
2025-06-22T17:42:51.470+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.LuxmedMedGatewayImpl       : Fetching event details for lab event 567399806
2025-06-22T17:42:51.663+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : GZIP compressed response detected, decompressing...
2025-06-22T17:42:51.664+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : Decompressed response length: 4630
2025-06-22T17:42:51.664+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : Original response length: 4630
2025-06-22T17:42:51.664+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : First 50 chars: {"examinations":[{"results":[{"name":"-","value":"
2025-06-22T17:42:51.665+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : Final sanitized response length: 4630
2025-06-22T17:42:51.665+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : First 100 chars after sanitization: {"examinations":[{"results":[{"name":"-","value":"-","measurement":"","minScopeFormatted":null,"maxS
2025-06-22T17:42:51.677+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.LuxmedMedGatewayImpl       : Received event details for lab event 567399806: eventType=3, status=4
2025-06-22T17:42:51.678+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Starting lab result mapping for event 0, eventType: 3
2025-06-22T17:42:51.679+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Event details raw data: title='Badanie laboratoryjne', date='2024-12-24T12:03:00', status=4
2025-06-22T17:42:51.679+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Converting EventDetailsResponse to LabResultResponse for event 0
2025-06-22T17:42:51.707+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : EventDetailsResponse JSON: {"id":0,"eventType":3,"status":4,"date":"2024-12-24T12:03:00","dateTo":null,"title":"Badanie laboratoryjne","description":null,"clinic":{"id":2521,"city":"KRAKÓW","address":"AL. POKOJU 18C","name":"LX Kraków - Al. Pokoju 18C (Parking płatny na terenie kompleksu - wewnętrzna strefa parkowania Fabryczna City)"},"doctor":null,"room":null,"floor":null,"referral":null,"recommendations":null,"actions":[],"payment":null,"service":null,"results":null,"examinations":[{"id":85754667,"name":"Morfologia + płytki + rozmaz automatyczny","result":null,"referenceRange":null,"unit":null,"isAbnormal":false,"notes":null}],"prescription":null}
2025-06-22T17:42:51.717+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Conversion successful: eventId=0, examinations.size=1
2025-06-22T17:42:51.717+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Successfully converted EventDetailsResponse to LabResultResponse for event 0
2025-06-22T17:42:51.719+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : LabResultResponse: eventId=0, examinations.size=1, title='Badanie laboratoryjne'
2025-06-22T17:42:51.719+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Starting mapLabResult for eventId: 0
2025-06-22T17:42:51.719+02:00  WARN 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Skipping lab result with eventId=0
2025-06-22T17:42:51.719+02:00  WARN 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Lab result mapping returned null for event 0
2025-06-22T17:42:51.720+02:00  WARN 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.LuxmedMedGatewayImpl       : Failed to map lab result for event 567399806. Event details: title='Badanie laboratoryjne', status=4
2025-06-22T17:42:51.720+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.LuxmedMedGatewayImpl       : Processing lab event 567399240: title='Badanie laboratoryjne', date='2024-12-24T11:51:00', status=4
2025-06-22T17:42:51.720+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.LuxmedMedGatewayImpl       : Fetching event details for lab event 567399240
2025-06-22T17:42:52.094+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : GZIP compressed response detected, decompressing...
2025-06-22T17:42:52.094+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : Decompressed response length: 2763
2025-06-22T17:42:52.095+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : Original response length: 2763
2025-06-22T17:42:52.095+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : First 50 chars: {"examinations":[{"results":[{"name":"Glukoza","va
2025-06-22T17:42:52.095+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : Final sanitized response length: 2763
2025-06-22T17:42:52.095+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.c.l.WebClientLuxMedClient    : First 100 chars after sanitization: {"examinations":[{"results":[{"name":"Glukoza","value":"4,78","measurement":"mmol/l","minScopeFormat
2025-06-22T17:42:52.095+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.LuxmedMedGatewayImpl       : Received event details for lab event 567399240: eventType=3, status=4
2025-06-22T17:42:52.095+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Starting lab result mapping for event 0, eventType: 3
2025-06-22T17:42:52.095+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Event details raw data: title='Badanie laboratoryjne', date='2024-12-24T11:51:00', status=4
2025-06-22T17:42:52.095+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Converting EventDetailsResponse to LabResultResponse for event 0
2025-06-22T17:42:52.096+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : EventDetailsResponse JSON: {"id":0,"eventType":3,"status":4,"date":"2024-12-24T11:51:00","dateTo":null,"title":"Badanie laboratoryjne","description":null,"clinic":{"id":2521,"city":"KRAKÓW","address":"AL. POKOJU 18C","name":"LX Kraków - Al. Pokoju 18C"},"doctor":null,"room":null,"floor":null,"referral":null,"recommendations":null,"actions":[],"payment":null,"service":null,"results":null,"examinations":[{"id":85756762,"name":"Glukoza / Glucose - na czczo","result":null,"referenceRange":null,"unit":null,"isAbnormal":false,"notes":null},{"id":85756763,"name":"Kreatynina / Creatinine","result":null,"referenceRange":null,"unit":null,"isAbnormal":false,"notes":null},{"id":85756760,"name":"T4 Wolne / Free - T4","result":null,"referenceRange":null,"unit":null,"isAbnormal":false,"notes":null},{"id":85756759,"name":"TSH / hTSH","result":null,"referenceRange":null,"unit":null,"isAbnormal":false,"notes":null}],"prescription":null}
2025-06-22T17:42:52.096+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Conversion successful: eventId=0, examinations.size=4
2025-06-22T17:42:52.096+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Successfully converted EventDetailsResponse to LabResultResponse for event 0
2025-06-22T17:42:52.096+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : LabResultResponse: eventId=0, examinations.size=4, title='Badanie laboratoryjne'
2025-06-22T17:42:52.096+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Starting mapLabResult for eventId: 0
2025-06-22T17:42:52.096+02:00  WARN 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Skipping lab result with eventId=0
2025-06-22T17:42:52.096+02:00  WARN 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.m.LuxMedLabResultMapper    : Lab result mapping returned null for event 0
2025-06-22T17:42:52.096+02:00  WARN 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.LuxmedMedGatewayImpl       : Failed to map lab result for event 567399240. Event details: title='Badanie laboratoryjne', status=4
2025-06-22T17:42:52.100+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.mapper.LuxMedEventMapper   : Skipping event type 6 for event 7666050
2025-06-22T17:42:52.100+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.mapper.LuxMedEventMapper   : Skipping event type 6 for event 7522256
2025-06-22T17:42:52.100+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.mapper.LuxMedEventMapper   : Skipping event type 6 for event 7445567
2025-06-22T17:42:52.101+02:00 DEBUG 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.mapper.LuxMedEventMapper   : Skipping event type 6 for event 7110941
2025-06-22T17:42:52.102+02:00  INFO 35549 --- [MyHealth-Service] [ctor-http-nio-3] c.j.m.s.h.d.g.LuxmedMedGatewayImpl       : Mapped LuxMed data for user 4f21e935-b145-48c2-a663-9dbc7c052777: 0 lab results, 14 visits
2025-06-22T17:42:52.180+02:00  INFO 35549 --- [MyHealth-Service] [       Thread-8] s.h.d.u.LuxmedDataSynchronizationUseCase : LuxMed data synchronization completed for user: 4f21e935-b145-48c2-a663-9dbc7c052777. Processed 0 lab results and 14 visits
2025-06-22T17:42:52.180+02:00  INFO 35549 --- [MyHealth-Service] [       Thread-8] c.j.m.s.h.a.c.LuxmedSyncController       : LuxMed synchronization completed successfully for user: 4f21e935-b145-48c2-a663-9dbc7c052777
